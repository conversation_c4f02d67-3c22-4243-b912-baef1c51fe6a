<?php
require_once '../config.php';
require_once '../db.php';

// Process registration
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $password = md5($_POST['password']);
    $role = mysqli_real_escape_string($conn, $_POST['role']);
    
    // Check if email already exists
    $checkEmail = "SELECT * FROM users WHERE email = '$email'";
    $result = $conn->query($checkEmail);
    
    if ($result->num_rows > 0) {
        $error = "Email already exists.";
    } else {
        // Insert new user
        $insertQuery = "INSERT INTO users (name, email, password, role) VALUES ('$name', '$email', '$password', '$role')";
        
        if ($conn->query($insertQuery) === TRUE) {
            // If investor is registering, set status to pending_investor
            if ($role == 'investor') {
                $userId = $conn->insert_id;
                $updateQuery = "UPDATE users SET role = 'pending_investor' WHERE user_id = $userId";
                $conn->query($updateQuery);
                
                $success = "Registration successful. Your account is pending approval by admin.";
            } else {
                $success = "Registration successful. You can now login.";
            }
        } else {
            $error = "Error: " . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Campus Shark Tank</title>
    <link rel="stylesheet" href="<?php echo CSS_PATH; ?>style.css">
</head>
<body>
    <div class="container">
        <div class="card" style="max-width: 500px; margin: 50px auto;">
            <h1>Register for Campus Shark Tank</h1>
            
            <?php if (isset($error)) { ?>
                <div class="error"><?php echo $error; ?></div>
            <?php } ?>
            
            <?php if (isset($success)) { ?>
                <div class="success"><?php echo $success; ?></div>
                <p><a href="login.php">Click here to login</a></p>
            <?php } else { ?>
                <form method="POST" action="">
                    <div class="form-group">
                        <label for="name">Full Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <div class="form-group">
                        <label for="role">Role</label>
                        <select id="role" name="role" required>
                            <option value="">Select Role</option>
                            <option value="student">Student</option>
                            <option value="investor">Investor</option>
                            <option value="mentor">Mentor</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <button type="submit" class="btn">Register</button>
                </form>
                
                <p style="margin-top: 20px;">Already have an account? <a href="login.php">Login</a></p>
            <?php } ?>
        </div>
    </div>
</body>
</html>