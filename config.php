<?php
// Site URL
define('SITE_URL', 'http://localhost/campus_sharktank/');

// Paths
define('ROOT_PATH', $_SERVER['DOCUMENT_ROOT'] . '/campus_sharktank/');
define('ASSETS_PATH', SITE_URL . 'assets/');
define('CSS_PATH', ASSETS_PATH . 'css/');
define('JS_PATH', ASSETS_PATH . 'js/');
define('UPLOADS_PATH', SITE_URL . 'uploads/');

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'campus_shark_tank');
define('DB_USER', 'root');
define('DB_PASS', 'root');
define('DB_CHARSET', 'utf8mb4');

/**
 * Database Connection Function
 * Returns a PDO connection object
 */
function getDBConnection() {
    static $pdo = null;

    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];

            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("Database connection failed: " . $e->getMessage());
        }
    }

    return $pdo;
}

// Session
session_start();
?>