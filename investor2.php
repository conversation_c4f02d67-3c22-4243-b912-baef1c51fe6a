<?php 
require_once 'db.php';  

// Get filter parameters
$category = isset($_GET['category']) ? $_GET['category'] : '';

// Build query
$query = "SELECT * FROM pitches WHERE status = 'active'";
if (!empty($category)) {
    $query .= " AND category = '" . $conn->real_escape_string($category) . "'";
}
$query .= " ORDER BY like_count DESC"; 

$res = $conn->query($query);

// Get unique categories for filter
$categoryQuery = "SELECT DISTINCT category FROM pitches WHERE status = 'active'";
$categoryResult = $conn->query($categoryQuery);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Campus Shark Tank - Investor Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --linkedin-blue: #0a66c2;
            --linkedin-light-blue: #e7f3ff;
            --linkedin-gray: #f3f2ef;
            --linkedin-dark-gray: #666666;
        }
        
        body {
            background-color: var(--linkedin-gray);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        
        .btn-primary {
            background-color: var(--linkedin-blue);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #004182;
        }
        
        .btn-secondary {
            background-color: #e0e0e0;
            color: #333;
        }
        
        .btn-secondary:hover {
            background-color: #d0d0d0;
        }
        
        .pitch-card {
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .pitch-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-active {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .status-inactive {
            background-color: #fee2e2;
            color: #b91c1c;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-40">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center">
                    <span class="text-white font-bold text-xl">CS</span>
                </div>
                <h1 class="text-xl font-bold text-gray-800">Campus Shark Tank</h1>
            </div>
            <nav>
                <ul class="flex space-x-6">
                    <li><a href="index.php" class="text-gray-700 hover:text-blue-600 font-medium">Dashboard</a></li>
                    <li><a href="faq.php" class="text-gray-700 hover:text-blue-600 font-medium">FAQ</a></li>
                    <li><a href="#" class="text-gray-700 hover:text-blue-600 font-medium">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-2xl font-bold text-gray-800">Investor Dashboard</h2>
            <div class="flex space-x-4">
                <div class="relative">
                    <form method="GET" action="">
                        <select name="category" class="bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Categories</option>
                            <?php while ($cat = $categoryResult->fetch_assoc()) { ?>
                                <option value="<?php echo $cat['category']; ?>" <?php echo ($category == $cat['category']) ? 'selected' : ''; ?>>
                                    <?php echo $cat['category']; ?>
                                </option>
                            <?php } ?>
                        </select>
                        <button type="submit" class="hidden"></button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Pitch Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php if ($res->num_rows > 0) { ?>
                <?php while ($pitch = $res->fetch_assoc()) { ?>
                    <div class="pitch-card bg-white rounded-lg shadow p-6">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-xl font-bold text-gray-800"><?php echo $pitch['title']; ?></h3>
                            <span class="status-badge status-<?php echo $pitch['status']; ?>">
                                <?php echo ucfirst($pitch['status']); ?>
                            </span>
                        </div>
                        <p class="text-gray-600 mb-2"><?php echo $pitch['description']; ?></p>
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm font-medium text-gray-500"><?php echo $pitch['category']; ?></span>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-medium text-gray-700">₹<?php echo $pitch['expected_amount']; ?> Lakh</span>
                                <span class="text-sm font-medium text-gray-700"><?php echo $pitch['expected_percent']; ?>%</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center space-x-2">
                                <form action="like_pitch.php" method="POST">
                                    <input type="hidden" name="pitch_id" value="<?php echo $pitch['pitch_id']; ?>">
                                    <button type="submit" class="flex items-center space-x-1 text-gray-500 hover:text-blue-600">
                                        <i class="fas fa-thumbs-up"></i>
                                        <span><?php echo $pitch['like_count']; ?></span>
                                    </button>
                                </form>
                            </div>
                            <a href="pitch_detail.php?id=<?php echo $pitch['pitch_id']; ?>" class="btn-primary py-2 px-4 rounded-md font-medium">View Details</a>
                        </div>
                    </div>
                <?php } ?>
            <?php } else { ?>
                <div class="col-span-3 text-center py-12">
                    <p class="text-gray-600 text-lg">No pitches found.</p>
                </div>
            <?php } ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2023 Campus Shark Tank. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>