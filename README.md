# Campus Shark Tank - Pitch Submission System

A comprehensive web application for students to submit their business pitches for a campus shark tank competition.

## Features

- **Responsive Form Design**: Modern, mobile-friendly interface
- **Comprehensive Data Collection**: Captures all essential pitch information
- **Real-time Validation**: Client-side and server-side validation
- **Auto-save Functionality**: Saves form data locally to prevent data loss
- **Database Integration**: Stores submissions in MySQL database
- **Success/Error Handling**: User-friendly feedback messages
- **Security Features**: Input sanitization and validation

## Database Schema

The system uses a MySQL database named `campus_shark_tank` with the following main table:

### Pitches Table
- `pitch_id` (INT, Primary Key, Auto-increment)
- `student_id` (INT, Required)
- `title` (VARCHAR(255), Required)
- `description` (TEXT, Required)
- `category` (VARCHAR(100), Required)
- `keywords` (VARCHAR(255))
- `video_url` (VARCHAR(255))
- `ppt_url` (VARCHAR(255))
- `poster_url` (VARCHAR(255))
- `revenue_lakhs` (DECIMAL(10,2))
- `expected_amount` (DECIMAL(10,2))
- `expected_percentage` (DECIMAL(5,2))
- `year_started` (INT)
- `strength` (TEXT)
- `weakness` (TEXT)
- `risk_factors` (TEXT)
- `date_uploaded` (DATETIME, Auto-generated)
- `status` (ENUM: 'active', 'inactive')
- `visibility` (ENUM: 'public', 'private')
- Auto-tracked fields for likes, dislikes, comments, and investor data

## Installation

### Prerequisites
- Web server (Apache/Nginx)
- PHP 7.4 or higher
- MySQL 5.7 or higher
- XAMPP/WAMP/LAMP (for local development)

### Setup Instructions

1. **Clone/Download Files**
   ```
   Place all files in your web server directory (e.g., htdocs for XAMPP)
   ```

2. **Database Setup**
   ```sql
   -- Run the database_setup.sql file in your MySQL client
   -- Or execute the following commands:
   
   CREATE DATABASE campus_shark_tank;
   USE campus_shark_tank;
   
   -- Then run the complete SQL from database_setup.sql
   ```

3. **Configure Database Connection**
   ```php
   // Edit config.php or submit_pitch.php
   $host = 'localhost';
   $dbname = 'campus_shark_tank';
   $username = 'your_db_username';  // Usually 'root' for local
   $password = 'your_db_password';  // Usually empty for local
   ```

4. **Set Permissions**
   ```
   Ensure web server has read/write permissions to the directory
   ```

5. **Access the Application**
   ```
   Open your browser and navigate to:
   http://localhost/your-project-folder/
   ```

## File Structure

```
campus-shark-tank/
├── index.html              # Main form page (static version)
├── submit_pitch.php         # PHP form with processing
├── style.css               # Styling and responsive design
├── script.js               # Client-side validation and enhancements
├── config.php              # Database configuration and utilities
├── database_setup.sql      # Database schema and sample data
└── README.md              # This file
```

## Usage

### For Students
1. Open the application in your web browser
2. Fill out the pitch submission form with required information:
   - Student ID (required)
   - Pitch Title (required)
   - Description (required)
   - Category (required)
   - Optional: Media URLs, financial data, business analysis
3. Submit the form
4. Receive confirmation with pitch ID

### For Administrators
- Access the database to view submitted pitches
- Use the provided SQL queries to analyze submissions
- Extend functionality as needed

## Form Sections

1. **Basic Information**
   - Student ID, Title, Description, Category, Keywords

2. **Media & Presentation**
   - Video URL, Presentation URL, Poster Image URL

3. **Financial Information**
   - Revenue, Expected Investment, Expected Percentage, Year Started

4. **Business Analysis**
   - Strengths, Weaknesses, Risk Factors

5. **Visibility Settings**
   - Status (Active/Inactive), Visibility (Public/Private)

## Validation Features

### Client-side Validation
- Required field validation
- URL format validation
- Numeric range validation
- Real-time error display
- Character counting for text areas

### Server-side Validation
- Input sanitization
- Data type validation
- SQL injection prevention
- XSS protection

## Security Features

- PDO prepared statements
- Input sanitization
- HTML entity encoding
- CSRF protection (in config.php)
- Error logging

## Customization

### Adding New Fields
1. Update the database schema in `database_setup.sql`
2. Add form fields in `submit_pitch.php`
3. Update the PHP processing logic
4. Add validation in `script.js`

### Styling Changes
- Modify `style.css` for visual customization
- Update CSS variables for color scheme changes
- Responsive breakpoints can be adjusted

### Database Configuration
- Update connection settings in `config.php`
- Modify table structure as needed
- Add indexes for performance optimization

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in config
   - Ensure MySQL service is running
   - Verify database exists

2. **Form Not Submitting**
   - Check PHP error logs
   - Verify file permissions
   - Ensure all required fields are filled

3. **Styling Issues**
   - Clear browser cache
   - Check CSS file path
   - Verify responsive viewport settings

### Error Logs
- Check PHP error logs for server-side issues
- Use browser developer tools for client-side debugging
- Enable error reporting in PHP for development

## Future Enhancements

- User authentication system
- File upload functionality
- Email notifications
- Admin dashboard
- Pitch voting system
- Investor portal
- Advanced search and filtering
- API endpoints for mobile apps

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review error logs
3. Verify database and file permissions
4. Test with sample data

## License

This project is open source and available under the MIT License.
