-- MySQL dump 10.10
--
-- Host: localhost    Database: db_shark_tank
-- ------------------------------------------------------
-- Server version	5.0.27-community-nt

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `pitch_comments`
--

DROP TABLE IF EXISTS `pitch_comments`;
CREATE TABLE `pitch_comments` (
  `id` int(11) NOT NULL auto_increment,
  `pitch_id` int(11) NOT NULL,
  `user_id` varchar(120) NOT NULL,
  `comment_text` text NOT NULL,
  `created_at` timestamp NOT NULL default CURRENT_TIMESTAMP,
  `to_user` varchar(120) default NULL,
  PRIMARY KEY  (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `pitch_comments`
--

LOCK TABLES `pitch_comments` WRITE;
/*!40000 ALTER TABLE `pitch_comments` DISABLE KEYS */;
INSERT INTO `pitch_comments` VALUES (1,1,'240213107005','Nice','2025-09-15 19:25:46',NULL),(2,1,'240213107005','Average','2025-09-15 19:46:03','240213107005'),(3,2,'240213107004','Nice','2025-09-15 20:01:22','240213107004'),(4,1,'240213107005','Good','2025-09-16 04:18:00','240213107005');
/*!40000 ALTER TABLE `pitch_comments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pitch_faqs`
--

DROP TABLE IF EXISTS `pitch_faqs`;
CREATE TABLE `pitch_faqs` (
  `faq_id` int(11) NOT NULL auto_increment,
  `pitch_id` int(11) NOT NULL,
  `question` text NOT NULL,
  `answer` text NOT NULL,
  PRIMARY KEY  (`faq_id`),
  KEY `pitch_id` (`pitch_id`),
  CONSTRAINT `pitch_faqs_ibfk_1` FOREIGN KEY (`pitch_id`) REFERENCES `pitches` (`pitch_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `pitch_faqs`
--

LOCK TABLES `pitch_faqs` WRITE;
/*!40000 ALTER TABLE `pitch_faqs` DISABLE KEYS */;
/*!40000 ALTER TABLE `pitch_faqs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pitch_investors`
--

DROP TABLE IF EXISTS `pitch_investors`;
CREATE TABLE `pitch_investors` (
  `investor_id` int(11) NOT NULL auto_increment,
  `pitch_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `company` varchar(255) default NULL,
  `amount` decimal(10,2) NOT NULL,
  `percentage` decimal(5,2) NOT NULL,
  PRIMARY KEY  (`investor_id`),
  KEY `pitch_id` (`pitch_id`),
  CONSTRAINT `pitch_investors_ibfk_1` FOREIGN KEY (`pitch_id`) REFERENCES `pitches` (`pitch_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `pitch_investors`
--

LOCK TABLES `pitch_investors` WRITE;
/*!40000 ALTER TABLE `pitch_investors` DISABLE KEYS */;
INSERT INTO `pitch_investors` VALUES (1,3,'Elon','Tesla','2.00','5.00');
/*!40000 ALTER TABLE `pitch_investors` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pitch_reactions`
--

DROP TABLE IF EXISTS `pitch_reactions`;
CREATE TABLE `pitch_reactions` (
  `id` int(11) NOT NULL auto_increment,
  `pitch_id` int(11) NOT NULL,
  `user_id` varchar(120) default NULL,
  `reaction_type` enum('like','dislike') NOT NULL,
  `created_at` timestamp NOT NULL default CURRENT_TIMESTAMP,
  PRIMARY KEY  (`id`),
  UNIQUE KEY `unique_user_pitch_reaction` (`user_id`,`pitch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `pitch_reactions`
--

LOCK TABLES `pitch_reactions` WRITE;
/*!40000 ALTER TABLE `pitch_reactions` DISABLE KEYS */;
/*!40000 ALTER TABLE `pitch_reactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pitches`
--

DROP TABLE IF EXISTS `pitches`;
CREATE TABLE `pitches` (
  `pitch_id` int(11) NOT NULL auto_increment,
  `student_id` varchar(20) default NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `category` varchar(100) default NULL,
  `keywords` varchar(255) default NULL,
  `video_url` varchar(255) default NULL,
  `ppt_url` varchar(255) default NULL,
  `poster_url` varchar(255) default NULL,
  `revenue_lakhs` decimal(10,2) default NULL,
  `expected_amount` decimal(10,2) default NULL,
  `expected_percent` decimal(5,2) default NULL,
  `year_started` int(11) default NULL,
  `strength` text,
  `weakness` text,
  `risk_factors` text,
  `date_uploaded` timestamp NOT NULL default CURRENT_TIMESTAMP,
  `status` enum('active','inactive') default 'active',
  `visibility` enum('public','private') default 'public',
  `student_percent` decimal(5,2) default NULL,
  `instagram_url` varchar(255) default NULL,
  `facebook_url` varchar(255) default NULL,
  `like_count` int(11) default '0',
  `dislike_count` int(11) default '0',
  PRIMARY KEY  (`pitch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `pitches`
--

LOCK TABLES `pitches` WRITE;
/*!40000 ALTER TABLE `pitches` DISABLE KEYS */;
INSERT INTO `pitches` VALUES (1,'240213107005','Demo','Demo Pitch','Tech','AI','https://www.youtube.com/watch?v=K4TOrB7at0Y','https://drive.google.com/file/d/138SqCtDqydzapMOGwUG9QM-s-F3JtOmS/view?usp=drive_link','https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR_lTsNc7egUl3kVbRl3WLE10X3HSS6QEzz3A&s','0.00','1.00','5.00',2025,'AI','AI','AI','2025-09-15 19:02:25','active','public','100.00','0','https://www.facebook.com',0,0),(2,'240213107004','Demo Pitch 1','This is my first demo pitch.\r\nI have worked a lot.','Education','AI','https://www.youtube.com/watch?v=K4TOrB7at0Y','https://drive.google.com/file/d/138SqCtDqydzapMOGwUG9QM-s-F3JtOmS/view?usp=drive_link','https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR_lTsNc7egUl3kVbRl3WLE10X3HSS6QEzz3A&s','75.00','5.00','2.00',2025,'HTML','CSS','JS','2025-09-15 20:00:01','active','public','100.00','0','https://www.facebook.com',0,0),(3,'240213107004','Demo Pitch 2','Demo Pitch 2','Social Impact','AI','https://www.youtube.com/watch?v=K4TOrB7at0Y','https://drive.google.com/file/d/138SqCtDqydzapMOGwUG9QM-s-F3JtOmS/view?usp=drive_link','https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR_lTsNc7egUl3kVbRl3WLE10X3HSS6QEzz3A&s','20.00','5.00','5.00',2025,'HTTP','HI','Hello','2025-09-16 04:29:23','active','public','95.00','0','https://www.facebook.com',0,0);
/*!40000 ALTER TABLE `pitches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `user_id` int(11) NOT NULL auto_increment,
  `role` enum('student','mentor','investor','admin') NOT NULL,
  `email` varchar(150) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) default NULL,
  `student_id` varchar(20) default NULL,
  `mentor_code` varchar(50) default NULL,
  `investor_wallet` varchar(255) default NULL,
  `admin_2fa` varchar(10) default NULL,
  `phone` varchar(15) default NULL,
  `verified` tinyint(1) default '0',
  `status` enum('active','inactive','banned') default 'active',
  `created_at` timestamp NOT NULL default CURRENT_TIMESTAMP,
  `wallet_fund` varchar(120) default '0',
  PRIMARY KEY  (`user_id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'student','<EMAIL>','$2y$10$GdUI9H1EpXeXmEgVvSuWg.UOHwhQYJt6uNlWTbasq8Ozkr4EgRON.','John Doe','STU123',NULL,NULL,NULL,'9876543210',1,'active','2025-09-13 13:05:27','0'),(2,'student','<EMAIL>','$2y$10$/CH1pF4y4zFG5AiEZvOYN.Ffgp7K/P/V2AGFYFlHkDO1x7YVhZXJ.','Nihal Joshi','240213107005',NULL,NULL,NULL,'1234567890',1,'active','2025-09-13 13:33:40','0'),(3,'student','<EMAIL>','$2y$10$.CE3TC0ziWQC5zG8YUdJeuOwZtmx7WXA5s.GNShr7FZGulnoQXFgC','Madhav Jani','240213107004',NULL,NULL,NULL,'5552223336',1,'active','2025-09-15 18:39:53','0'),(4,'admin','<EMAIL>','$2y$10$.CE3TC0ziWQC5zG8YUdJeuOwZtmx7WXA5s.GNShr7FZGulnoQXFgC','admin',NULL,NULL,NULL,'291205',NULL,1,'active','2025-09-15 20:52:25','0');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-09-16  4:50:27
