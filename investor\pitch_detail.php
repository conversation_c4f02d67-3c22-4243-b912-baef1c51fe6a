<?php
require_once '../config.php';
require_once '../db.php';
require_once '../includes/header.php';

// Check if investor is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'investor') {
    header('Location: ../auth/login.php');
    exit;
}

// Get pitch ID
$pitch_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($pitch_id <= 0) {
    header('Location: dashboard.php');
    exit;
}

// Get pitch details
$query = "SELECT * FROM pitches WHERE pitch_id = $pitch_id AND status = 'approved'";
$result = $conn->query($query);

if ($result->num_rows == 0) {
    header('Location: dashboard.php');
    exit;
}

$pitch = $result->fetch_assoc();

// Get founder details
$founder_id = $pitch['user_id'];
$founderQuery = "SELECT name, email FROM users WHERE user_id = $founder_id";
$founderResult = $conn->query($founderQuery);
$founder = $founderResult->fetch_assoc();

// Get previous funding
$fundingQuery = "SELECT * FROM transactions WHERE pitch_id = $pitch_id";
$fundingResult = $conn->query($fundingQuery);

// Get feedback
$feedbackQuery = "SELECT f.feedback_text, u.name FROM feedback f 
                  JOIN users u ON f.user_id = u.user_id 
                  WHERE f.pitch_id = $pitch_id 
                  ORDER BY f.created_at DESC";
$feedbackResult = $conn->query($feedbackQuery);
?>

<div class="container">
    <div class="card">
        <h1><?php echo $pitch['title']; ?></h1>
        <p><strong>Category:</strong> <?php echo $pitch['category']; ?></p>
        <p><strong>Founder:</strong> <?php echo $founder['name']; ?> (<?php echo $founder['email']; ?>)</p>
        <p><strong>Seeking Amount:</strong> $<?php echo number_format($pitch['funding_goal'], 2); ?></p>
        <p><strong>Offered Equity:</strong> <?php echo $pitch['equity_offered']; ?>%</p>
        <p><strong>Likes:</strong> <?php echo $pitch['likes']; ?></p>
        
        <h2>Description</h2>
        <p><?php echo nl2br($pitch['description']); ?></p>
        
        <h2>Problem Statement</h2>
        <p><?php echo nl2br($pitch['problem']); ?></p>
        
        <h2>Solution</h2>
        <p><?php echo nl2br($pitch['solution']); ?></p>
        
        <?php if (!empty($pitch['video_link'])) { ?>
            <h2>Pitch Video</h2>
            <div class="video-container">
                <iframe width="560" height="315" src="<?php echo $pitch['video_link']; ?>" frameborder="0" allowfullscreen></iframe>
            </div>
        <?php } ?>
        
        <?php if (!empty($pitch['pitch_file'])) { ?>
            <h2>Proof of Concept</h2>
            <a href="<?php echo UPLOADS_PATH . 'pitches/' . $pitch['pitch_file']; ?>" class="btn" target="_blank">View Document</a>
        <?php } ?>
        
        <h2>Previous Funding</h2>
        <?php if ($fundingResult->num_rows > 0) { ?>
            <table class="data-table">
                <tr>
                    <th>Investor</th>
                    <th>Amount</th>
                    <th>Date</th>
                </tr>
                <?php while ($funding = $fundingResult->fetch_assoc()) { 
                    $investorQuery = "SELECT name FROM users WHERE user_id = " . $funding['from_user'];
                    $investorResult = $conn->query($investorQuery);
                    $investor = $investorResult->fetch_assoc();
                ?>
                    <tr>
                        <td><?php echo $investor['name']; ?></td>
                        <td>$<?php echo number_format($funding['amount'], 2); ?></td>
                        <td><?php echo date('M d, Y', strtotime($funding['txn_time'])); ?></td>
                    </tr>
                <?php } ?>
            </table>
        <?php } else { ?>
            <p>No previous funding.</p>
        <?php } ?>
        
        <div class="actions">
            <a href="negotiate.php?pitch_id=<?php echo $pitch_id; ?>" class="btn">Make an Offer</a>
            <a href="feedback.php?pitch_id=<?php echo $pitch_id; ?>" class="btn btn-secondary">Provide Feedback</a>
            <a href="dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
        </div>
        
        <!-- Like Button -->
        <div class="like-section">
            <form method="POST" action="like_pitch.php">
                <input type="hidden" name="pitch_id" value="<?php echo $pitch_id; ?>">
                <button type="submit" class="btn btn-like">
                    <i class="fas fa-thumbs-up"></i> Like (<span id="like-count"><?php echo $pitch['likes']; ?></span>)
                </button>
            </form>
        </div>
    </div>
    
    <div class="card">
        <h2>Feedback</h2>
        <?php if ($feedbackResult->num_rows > 0) { ?>
            <?php while ($feedback = $feedbackResult->fetch_assoc()) { ?>
                <div class="feedback-item">
                    <h4><?php echo $feedback['name']; ?></h4>
                    <p><?php echo nl2br($feedback['feedback_text']); ?></p>
                </div>
            <?php } ?>
        <?php } else { ?>
            <p>No feedback yet.</p>
        <?php } ?>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>