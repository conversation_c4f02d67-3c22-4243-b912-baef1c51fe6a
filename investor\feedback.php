<?php
require_once '../config.php';
require_once '../db.php';
require_once '../includes/header.php';

// Check if investor is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'investor') {
    header('Location: ../auth/login.php');
    exit;
}

// Get pitch ID
$pitch_id = isset($_GET['pitch_id']) ? (int)$_GET['pitch_id'] : 0;

if ($pitch_id <= 0) {
    header('Location: dashboard.php');
    exit;
}

// Get pitch details
$query = "SELECT * FROM pitches WHERE pitch_id = $pitch_id AND status = 'approved'";
$result = $conn->query($query);

if ($result->num_rows == 0) {
    header('Location: dashboard.php');
    exit;
}

$pitch = $result->fetch_assoc();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $feedback = mysqli_real_escape_string($conn, $_POST['feedback']);
    $investor_id = $_SESSION['user_id'];
    
    if (empty($feedback)) {
        $error = "Please provide your feedback.";
    } else {
        $insertQuery = "INSERT INTO feedback (user_id, pitch_id, feedback_text) 
                        VALUES ($investor_id, $pitch_id, '$feedback')";
        
        if ($conn->query($insertQuery) === TRUE) {
            $success = "Your feedback has been submitted.";
        } else {
            $error = "Error: " . $conn->error;
        }
    }
}
?>

<div class="container">
    <div class="card">
        <h1>Provide Feedback</h1>
        <h2><?php echo $pitch['title']; ?></h2>
        
        <?php if (isset($error)) { ?>
            <div class="error"><?php echo $error; ?></div>
        <?php } ?>
        
        <?php if (isset($success)) { ?>
            <div class="success"><?php echo $success; ?></div>
            <a href="pitch_detail.php?id=<?php echo $pitch_id; ?>" class="btn">Back to Pitch</a>
        <?php } else { ?>
            <form method="POST" action="">
                <div class="form-group">
                    <label for="feedback">Your Feedback</label>
                    <textarea id="feedback" name="feedback" required></textarea>
                </div>
                <button type="submit" class="btn">Submit Feedback</button>
                <a href="pitch_detail.php?id=<?php echo $pitch_id; ?>" class="btn btn-secondary">Cancel</a>
            </form>
        <?php } ?>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>