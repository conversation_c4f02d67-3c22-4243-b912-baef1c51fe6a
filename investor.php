<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Campus Shark Tank - Investor Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* LinkedIn-like custom styles */
        :root {
            --linkedin-blue: #0a66c2;
            --linkedin-light-blue: #e7f3ff;
            --linkedin-gray: #f3f2ef;
            --linkedin-dark-gray: #666666;
        }
        
        body {
            background-color: var(--linkedin-gray);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        
        .btn-primary {
            background-color: var(--linkedin-blue);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #004182;
        }
        
        .btn-secondary {
            background-color: #e0e0e0;
            color: #333;
        }
        
        .btn-secondary:hover {
            background-color: #d0d0d0;
        }
        
        .pitch-card {
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .pitch-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .like-btn.liked {
            color: var(--linkedin-blue);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 50;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .status-approved {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .status-rejected {
            background-color: #fee2e2;
            color: #b91c1c;
        }
        
        .status-funded {
            background-color: #dbeafe;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-40">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center">
                    <span class="text-white font-bold text-xl">CS</span>
                </div>
                <h1 class="text-xl font-bold text-gray-800">Campus Shark Tank</h1>
            </div>
            <nav>
                <ul class="flex space-x-6">
                    <li><a href="#" class="text-gray-700 hover:text-blue-600 font-medium">Dashboard</a></li>
                    <li><a href="#" class="text-gray-700 hover:text-blue-600 font-medium" onclick="showFAQ()">FAQ</a></li>
                    <li><a href="#" class="text-gray-700 hover:text-blue-600 font-medium">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Dashboard Section -->
        <section id="dashboard" class="mb-12">
            <div class="flex justify-between items-center mb-8">
                <h2 class="text-2xl font-bold text-gray-800">Investor Dashboard</h2>
                <div class="flex space-x-4">
                    <div class="relative">
                        <select id="categoryFilter" class="bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Categories</option>
                            <option value="Technology">Technology</option>
                            <option value="Healthcare">Healthcare</option>
                            <option value="Education">Education</option>
                            <option value="Sustainability">Sustainability</option>
                            <option value="Finance">Finance</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Pitch Cards -->
            <div id="pitchContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Pitch cards will be dynamically inserted here -->
            </div>
        </section>

        <!-- FAQ Section -->
        <section id="faqSection" class="hidden mb-12">
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">Frequently Asked Questions</h2>
                
                <div class="space-y-6">
                    <div class="border-b pb-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">How do I invest in a pitch?</h3>
                        <p class="text-gray-600">To invest in a pitch, first browse the available pitches on the dashboard. Click on a pitch to view details, then click "Make an Offer". Enter your investment amount and desired equity percentage, then submit your offer. The entrepreneur will review your offer and respond accordingly.</p>
                    </div>
                    
                    <div class="border-b pb-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">What happens after I make an offer?</h3>
                        <p class="text-gray-600">After you submit an offer, the entrepreneur will receive a notification. They can accept, reject, or counter your offer. You'll be notified of their decision. If they accept, you can proceed with the payment process.</p>
                    </div>
                    
                    <div class="border-b pb-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">How is the equity calculated?</h3>
                        <p class="text-gray-600">The equity percentage you receive is based on the amount you invest relative to the total funding goal. For example, if a startup is seeking $100,000 for 10% equity, and you invest $10,000, you would receive 1% equity in the company.</p>
                    </div>
                    
                    <div class="border-b pb-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">Can I negotiate the terms?</h3>
                        <p class="text-gray-600">Yes, you can propose different terms than what the entrepreneur initially offered. The entrepreneur can then accept, reject, or make a counter-offer. This negotiation process continues until both parties agree or one party withdraws.</p>
                    </div>
                    
                    <div class="border-b pb-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">How do I know if my investment is secure?</h3>
                        <p class="text-gray-600">While we strive to verify all pitches and entrepreneurs, investing in startups always carries risk. We recommend conducting your own due diligence before making any investment decisions. Our platform facilitates connections but does not guarantee investment returns.</p>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">Can I provide feedback without investing?</h3>
                        <p class="text-gray-600">Yes, you can provide feedback on any pitch without making an investment. Simply click on a pitch and use the "Provide Feedback" option to share your thoughts with the entrepreneur.</p>
                    </div>
                </div>
                
                <div class="mt-8">
                    <button onclick="showDashboard()" class="btn-primary py-2 px-6 rounded-md font-medium">Back to Dashboard</button>
                </div>
            </div>
        </section>
    </main>

    <!-- Pitch Detail Modal -->
    <div id="pitchModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-start mb-4">
                    <h2 id="modalTitle" class="text-2xl font-bold text-gray-800"></h2>
                    <button onclick="closePitchModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <div id="modalContent" class="space-y-6">
                    <!-- Pitch details will be dynamically inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Negotiation Modal -->
    <div id="negotiationModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-start mb-4">
                    <h2 class="text-xl font-bold text-gray-800">Make an Offer</h2>
                    <button onclick="closeNegotiationModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <form id="negotiationForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Your Investment Amount ($)</label>
                        <input type="number" id="investmentAmount" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" min="1" step="0.01" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Equity You Want (%)</label>
                        <input type="number" id="equityAmount" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" min="0.1" max="100" step="0.1" required>
                    </div>
                    
                    <div class="flex space-x-3 pt-2">
                        <button type="submit" class="btn-primary py-2 px-4 rounded-md font-medium flex-1">Submit Offer</button>
                        <button type="button" onclick="closeNegotiationModal()" class="btn-secondary py-2 px-4 rounded-md font-medium flex-1">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Feedback Modal -->
    <div id="feedbackModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-start mb-4">
                    <h2 class="text-xl font-bold text-gray-800">Provide Feedback</h2>
                    <button onclick="closeFeedbackModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <form id="feedbackForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Your Feedback</label>
                        <textarea id="feedbackText" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required></textarea>
                    </div>
                    
                    <div class="flex space-x-3 pt-2">
                        <button type="submit" class="btn-primary py-2 px-4 rounded-md font-medium flex-1">Submit Feedback</button>
                        <button type="button" onclick="closeFeedbackModal()" class="btn-secondary py-2 px-4 rounded-md font-medium flex-1">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2023 Campus Shark Tank. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Mock data for pitches
        const pitches = [
            {
                id: 1,
                title: "EcoPack",
                category: "Sustainability",
                description: "Biodegradable packaging solution for food delivery services that decomposes within 30 days.",
                problem: "Current food packaging creates massive waste that takes hundreds of years to decompose.",
                solution: "Our plant-based packaging materials decompose naturally within a month while maintaining food freshness.",
                videoLink: "https://www.youtube.com/embed/dQw4w9WgXcQ",
                documentLink: "#",
                founder: "Jane Smith",
                founderEmail: "<EMAIL>",
                fundingGoal: 50000,
                equityOffered: 10,
                currentRaised: 15000,
                likes: 24,
                liked: false,
                status: "approved",
                previousFunding: [
                    { investor: "Green Ventures", amount: 10000, date: "2023-05-15" },
                    { investor: "EcoFund", amount: 5000, date: "2023-06-20" }
                ],
                feedback: [
                    { name: "John Doe", text: "Great concept with huge market potential!" },
                    { name: "Sarah Johnson", text: "I love the environmental impact. Would invest if terms are right." }
                ]
            },
            {
                id: 2,
                title: "StudyBuddy",
                category: "Education",
                description: "AI-powered study assistant that helps students create personalized study plans and track progress.",
                problem: "Students struggle with effective time management and personalized learning paths.",
                solution: "Our AI analyzes learning patterns and creates customized study schedules with progress tracking.",
                videoLink: "https://www.youtube.com/embed/dQw4w9WgXcQ",
                documentLink: "#",
                founder: "Alex Johnson",
                founderEmail: "<EMAIL>",
                fundingGoal: 75000,
                equityOffered: 15,
                currentRaised: 25000,
                likes: 18,
                liked: false,
                status: "pending",
                previousFunding: [
                    { investor: "EdTech Ventures", amount: 15000, date: "2023-07-10" },
                    { investor: "Future Fund", amount: 10000, date: "2023-08-05" }
                ],
                feedback: [
                    { name: "Michael Brown", text: "The AI algorithm needs improvement but the concept is solid." },
                    { name: "Emily Davis", text: "As a teacher, I see great potential for this in classrooms." }
                ]
            },
            {
                id: 3,
                title: "HealthTrack",
                category: "Healthcare",
                description: "Wearable device that monitors vital signs and predicts potential health issues before they become serious.",
                problem: "Many health issues go undetected until they become serious problems.",
                solution: "Our wearable device continuously monitors vital signs and uses AI to predict potential health issues.",
                videoLink: "https://www.youtube.com/embed/dQw4w9WgXcQ",
                documentLink: "#",
                founder: "Robert Chen",
                founderEmail: "<EMAIL>",
                fundingGoal: 100000,
                equityOffered: 12,
                currentRaised: 45000,
                likes: 32,
                liked: true,
                status: "funded",
                previousFunding: [
                    { investor: "MedTech Capital", amount: 25000, date: "2023-04-12" },
                    { investor: "Health Innovations", amount: 20000, date: "2023-05-30" }
                ],
                feedback: [
                    { name: "Dr. Lisa Wang", text: "Impressive technology with real potential to save lives." },
                    { name: "James Wilson", text: "The predictive accuracy needs more validation but promising." }
                ]
            },
            {
                id: 4,
                title: "FinSecure",
                category: "Finance",
                description: "AI-powered financial advisor that provides personalized investment advice for young professionals.",
                problem: "Young professionals lack access to affordable financial advice tailored to their situation.",
                solution: "Our platform uses AI to provide personalized investment recommendations based on individual goals and risk tolerance.",
                videoLink: "https://www.youtube.com/embed/dQw4w9WgXcQ",
                documentLink: "#",
                founder: "Michelle Lee",
                founderEmail: "<EMAIL>",
                fundingGoal: 60000,
                equityOffered: 8,
                currentRaised: 20000,
                likes: 15,
                liked: false,
                status: "rejected",
                previousFunding: [
                    { investor: "FinTech Partners", amount: 10000, date: "2023-06-18" },
                    { investor: "Startup Capital", amount: 10000, date: "2023-07-22" }
                ],
                feedback: [
                    { name: "David Thompson", text: "The market is crowded but your approach has merit." },
                    { name: "Jennifer Garcia", text: "Need more differentiation from existing robo-advisors." }
                ]
            },
            {
                id: 5,
                title: "AgriTech",
                category: "Sustainability",
                description: "Smart farming solution that uses IoT sensors to optimize water usage and crop yields.",
                problem: "Traditional farming methods waste water and resources while producing lower yields.",
                solution: "Our IoT sensors monitor soil conditions and weather to optimize irrigation and fertilization.",
                videoLink: "https://www.youtube.com/embed/dQw4w9WgXcQ",
                documentLink: "#",
                founder: "Carlos Rodriguez",
                founderEmail: "<EMAIL>",
                fundingGoal: 80000,
                equityOffered: 15,
                currentRaised: 30000,
                likes: 21,
                liked: false,
                status: "approved",
                previousFunding: [
                    { investor: "GreenTech Fund", amount: 15000, date: "2023-05-05" },
                    { investor: "Sustainable Ventures", amount: 15000, date: "2023-06-15" }
                ],
                feedback: [
                    { name: "Thomas Anderson", text: "Great solution for water conservation in agriculture." },
                    { name: "Maria Gonzalez", text: "The ROI for farmers needs to be clearly demonstrated." }
                ]
            },
            {
                id: 6,
                title: "CodeMaster",
                category: "Education",
                description: "Interactive coding platform that teaches programming through gamification and real-world projects.",
                problem: "Learning to code is often boring and disconnected from real applications.",
                solution: "Our platform uses gamification and real projects to make coding engaging and practical.",
                videoLink: "https://www.youtube.com/embed/dQw4w9WgXcQ",
                documentLink: "#",
                founder: "Daniel Kim",
                founderEmail: "<EMAIL>",
                fundingGoal: 40000,
                equityOffered: 10,
                currentRaised: 10000,
                likes: 19,
                liked: true,
                status: "pending",
                previousFunding: [
                    { investor: "EdTech Angels", amount: 5000, date: "2023-07-01" },
                    { investor: "Learn Capital", amount: 5000, date: "2023-08-10" }
                ],
                feedback: [
                    { name: "Sophie Martin", text: "The gamification aspect is brilliant for keeping learners engaged." },
                    { name: "Ryan Lee", text: "Would like to see more advanced programming languages supported." }
                ]
            }
        ];

        // Current pitch ID for modals
        let currentPitchId = null;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            renderPitches(pitches);
            
            // Add event listener for category filter
            document.getElementById('categoryFilter').addEventListener('change', function() {
                const category = this.value;
                const filteredPitches = category ? pitches.filter(p => p.category === category) : pitches;
                renderPitches(filteredPitches);
            });
            
            // Add event listener for negotiation form
            document.getElementById('negotiationForm').addEventListener('submit', function(e) {
                e.preventDefault();
                handleNegotiationSubmit();
            });
            
            // Add event listener for feedback form
            document.getElementById('feedbackForm').addEventListener('submit', function(e) {
                e.preventDefault();
                handleFeedbackSubmit();
            });
        });

        // Render pitch cards
        function renderPitches(pitchesToRender) {
            const container = document.getElementById('pitchContainer');
            container.innerHTML = '';
            
            // Sort by likes (most liked first)
            const sortedPitches = [...pitchesToRender].sort((a, b) => b.likes - a.likes);
            
            sortedPitches.forEach(pitch => {
                const card = document.createElement('div');
                card.className = 'pitch-card bg-white rounded-lg shadow p-6';
                
                card.innerHTML = `
                    <div class="flex justify-between items-start mb-4">
                        <h3 class="text-xl font-bold text-gray-800">${pitch.title}</h3>
                        <span class="status-badge status-${pitch.status}">${pitch.status.charAt(0).toUpperCase() + pitch.status.slice(1)}</span>
                    </div>
                    <p class="text-gray-600 mb-2">${pitch.description}</p>
                    <div class="flex justify-between items-center mb-4">
                        <span class="text-sm font-medium text-gray-500">${pitch.category}</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium text-gray-700">$${pitch.fundingGoal.toLocaleString()}</span>
                            <span class="text-sm font-medium text-gray-700">${pitch.equityOffered}%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center space-x-2">
                            <button onclick="toggleLike(${pitch.id})" class="like-btn ${pitch.liked ? 'liked' : ''} flex items-center space-x-1 text-gray-500 hover:text-blue-600">
                                <i class="fas fa-thumbs-up"></i>
                                <span id="likes-${pitch.id}">${pitch.likes}</span>
                            </button>
                        </div>
                        <button onclick="showPitchDetail(${pitch.id})" class="btn-primary py-2 px-4 rounded-md font-medium">View Details</button>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }

        // Toggle like on a pitch
        function toggleLike(pitchId) {
            const pitch = pitches.find(p => p.id === pitchId);
            if (pitch) {
                pitch.liked = !pitch.liked;
                pitch.likes += pitch.liked ? 1 : -1;
                
                // Update UI
                const likeBtn = document.querySelector(`#likes-${pitchId}`).parentElement;
                likeBtn.classList.toggle('liked', pitch.liked);
                document.getElementById(`likes-${pitchId}`).textContent = pitch.likes;
                
                // Re-render to update sorting
                const category = document.getElementById('categoryFilter').value;
                const filteredPitches = category ? pitches.filter(p => p.category === category) : pitches;
                renderPitches(filteredPitches);
            }
        }

        // Show pitch detail modal
        function showPitchDetail(pitchId) {
            currentPitchId = pitchId;
            const pitch = pitches.find(p => p.id === pitchId);
            
            if (pitch) {
                document.getElementById('modalTitle').textContent = pitch.title;
                
                const modalContent = document.getElementById('modalContent');
                modalContent.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-4">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">Description</h3>
                                <p class="text-gray-600">${pitch.description}</p>
                            </div>
                            
                            <div class="mb-4">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">Problem Statement</h3>
                                <p class="text-gray-600">${pitch.problem}</p>
                            </div>
                            
                            <div class="mb-4">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">Solution</h3>
                                <p class="text-gray-600">${pitch.solution}</p>
                            </div>
                            
                            <div class="mb-4">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">Founder</h3>
                                <p class="text-gray-600">${pitch.founder} (${pitch.founderEmail})</p>
                            </div>
                        </div>
                        
                        <div>
                            <div class="mb-4">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">Funding Details</h3>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <div class="flex justify-between mb-2">
                                        <span class="text-gray-600">Seeking Amount:</span>
                                        <span class="font-medium">$${pitch.fundingGoal.toLocaleString()}</span>
                                    </div>
                                    <div class="flex justify-between mb-2">
                                        <span class="text-gray-600">Offered Equity:</span>
                                        <span class="font-medium">${pitch.equityOffered}%</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Current Raised:</span>
                                        <span class="font-medium">$${pitch.currentRaised.toLocaleString()}</span>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                                            <div class="bg-blue-600 h-2.5 rounded-full" style="width: ${(pitch.currentRaised / pitch.fundingGoal) * 100}%"></div>
                                        </div>
                                        <div class="text-right text-sm text-gray-500 mt-1">
                                            ${Math.round((pitch.currentRaised / pitch.fundingGoal) * 100)}% funded
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">Pitch Video</h3>
                                <div class="aspect-w-16 aspect-h-9">
                                    <iframe class="w-full h-64 rounded-lg" src="${pitch.videoLink}" frameborder="0" allowfullscreen></iframe>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">Proof of Concept</h3>
                                <a href="${pitch.documentLink}" class="btn-secondary inline-block py-2 px-4 rounded-md font-medium" target="_blank">
                                    <i class="fas fa-file-pdf mr-2"></i> View Document
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Previous Funding</h3>
                        ${pitch.previousFunding.length > 0 ? `
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Investor</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        ${pitch.previousFunding.map(funding => `
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">${funding.investor}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">$${funding.amount.toLocaleString()}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">${new Date(funding.date).toLocaleDateString()}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        ` : '<p class="text-gray-600">No previous funding.</p>'}
                    </div>
                    
                    <div class="mt-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Feedback</h3>
                        ${pitch.feedback.length > 0 ? `
                            <div class="space-y-4">
                                ${pitch.feedback.map(fb => `
                                    <div class="border-l-4 border-blue-500 pl-4 py-1">
                                        <h4 class="font-medium text-gray-800">${fb.name}</h4>
                                        <p class="text-gray-600">${fb.text}</p>
                                    </div>
                                `).join('')}
                            </div>
                        ` : '<p class="text-gray-600">No feedback yet.</p>'}
                    </div>
                    
                    <div class="flex flex-wrap gap-3 mt-8">
                        <button onclick="showNegotiationModal()" class="btn-primary py-2 px-6 rounded-md font-medium">
                            <i class="fas fa-handshake mr-2"></i> Make an Offer
                        </button>
                        <button onclick="showFeedbackModal()" class="btn-secondary py-2 px-6 rounded-md font-medium">
                            <i class="fas fa-comment mr-2"></i> Provide Feedback
                        </button>
                        <button onclick="toggleLike(${pitch.id})" class="btn-secondary py-2 px-6 rounded-md font-medium">
                            <i class="fas fa-thumbs-up mr-2"></i> Like (<span id="modal-likes">${pitch.likes}</span>)
                        </button>
                    </div>
                `;
                
                document.getElementById('pitchModal').classList.add('active');
            }
        }

        // Close pitch detail modal
        function closePitchModal() {
            document.getElementById('pitchModal').classList.remove('active');
        }

        // Show negotiation modal
        function showNegotiationModal() {
            document.getElementById('negotiationModal').classList.add('active');
        }

        // Close negotiation modal
        function closeNegotiationModal() {
            document.getElementById('negotiationModal').classList.remove('active');
            document.getElementById('negotiationForm').reset();
        }

        // Handle negotiation form submission
        function handleNegotiationSubmit() {
            const amount = document.getElementById('investmentAmount').value;
            const equity = document.getElementById('equityAmount').value;
            
            // In a real app, this would send data to a backend
            alert(`Your offer of $${amount} for ${equity}% equity has been sent to the entrepreneur.`);
            closeNegotiationModal();
        }

        // Show feedback modal
        function showFeedbackModal() {
            document.getElementById('feedbackModal').classList.add('active');
        }

        // Close feedback modal
        function closeFeedbackModal() {
            document.getElementById('feedbackModal').classList.remove('active');
            document.getElementById('feedbackForm').reset();
        }

        // Handle feedback form submission
        function handleFeedbackSubmit() {
            const feedback = document.getElementById('feedbackText').value;
            
            // In a real app, this would send data to a backend
            alert('Your feedback has been submitted successfully.');
            closeFeedbackModal();
        }

        // Show FAQ section
        function showFAQ() {
            document.getElementById('dashboard').classList.add('hidden');
            document.getElementById('faqSection').classList.remove('hidden');
        }

        // Show dashboard
        function showDashboard() {
            document.getElementById('faqSection').classList.add('hidden');
            document.getElementById('dashboard').classList.remove('hidden');
        }
    </script>
</body>
</html>