/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f3f2ef;
    color: #000000;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Header Styles */
header {
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo a {
    font-size: 24px;
    font-weight: bold;
    color: #0a66c2;
    text-decoration: none;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    text-decoration: none;
    color: #000000;
    font-weight: 500;
    transition: color 0.3s;
}

nav ul li a:hover {
    color: #0a66c2;
}

/* Main Content */
main {
    padding: 30px 0;
}

/* Card Styles */
.card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #0a66c2;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #004182;
}

.btn-secondary {
    background-color: #e0e0e0;
    color: #000000;
}

.btn-secondary:hover {
    background-color: #d0d0d0;
}

/* Form Styles */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 16px;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Pitch Card */
.pitch-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    background-color: #fff;
    transition: transform 0.3s, box-shadow 0.3s;
}

.pitch-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.pitch-card h3 {
    margin-bottom: 10px;
    color: #0a66c2;
}

.pitch-card p {
    margin-bottom: 5px;
}

.pitch-card .actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

/* Like Button */
.btn-like {
    background-color: #e0e0e0;
    color: #000000;
}

.btn-like:hover {
    background-color: #0a66c2;
    color: #fff;
}

/* Feedback Item */
.feedback-item {
    border-bottom: 1px solid #e0e0e0;
    padding: 15px 0;
}

.feedback-item:last-child {
    border-bottom: none;
}

.feedback-item h4 {
    margin-bottom: 5px;
    color: #0a66c2;
}

/* FAQ Item */
.faq-item {
    margin-bottom: 30px;
}

.faq-item h3 {
    margin-bottom: 10px;
    color: #0a66c2;
}

/* Data Table */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.data-table th, .data-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.data-table th {
    background-color: #f3f2ef;
    font-weight: 600;
}

/* Video Container */
.video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    margin-bottom: 20px;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Error and Success Messages */
.error, .success {
    padding: 10px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.error {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ef9a9a;
}

.success {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

/* Footer Styles */
footer {
    background-color: #000000;
    color: #fff;
    text-align: center;
    padding: 20px 0;
    margin-top: 50px;
}

/* Responsive */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
    }

    nav ul {
        margin-top: 15px;
    }

    nav ul li {
        margin: 0 10px;
    }
}