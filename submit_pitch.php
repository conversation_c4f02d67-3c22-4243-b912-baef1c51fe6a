<?php
// Database configuration
$host = 'localhost';
$dbname = 'campus_shark_tank';
$username = 'root'; // Change this to your database username
$password = '';     // Change this to your database password

// Create connection
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Sanitize and validate input data
        $student_id = filter_input(INPUT_POST, 'student_id', FILTER_VALIDATE_INT);
        $title = trim($_POST['title']);
        $description = trim($_POST['description']);
        $category = trim($_POST['category']);
        $keywords = trim($_POST['keywords']);
        $video_url = filter_input(INPUT_POST, 'video_url', FILTER_VALIDATE_URL) ?: null;
        $ppt_url = filter_input(INPUT_POST, 'ppt_url', FILTER_VALIDATE_URL) ?: null;
        $poster_url = filter_input(INPUT_POST, 'poster_url', FILTER_VALIDATE_URL) ?: null;
        $revenue_lakhs = filter_input(INPUT_POST, 'revenue_lakhs', FILTER_VALIDATE_FLOAT) ?: null;
        $expected_amount = filter_input(INPUT_POST, 'expected_amount', FILTER_VALIDATE_FLOAT) ?: null;
        $expected_percentage = filter_input(INPUT_POST, 'expected_percentage', FILTER_VALIDATE_FLOAT) ?: null;
        $year_started = filter_input(INPUT_POST, 'year_started', FILTER_VALIDATE_INT) ?: null;
        $strength = trim($_POST['strength']);
        $weakness = trim($_POST['weakness']);
        $risk_factors = trim($_POST['risk_factors']);
        $status = $_POST['status'] ?? 'active';
        $visibility = $_POST['visibility'] ?? 'public';
        
        // Current date and time
        $date_uploaded = date('Y-m-d H:i:s');
        
        // Initialize auto-tracked fields
        $student_percentage = 100.00; // Default ownership percentage
        $no_of_investors = 0;
        $no_of_investors_accepted = 0;
        $likes = 0;
        $dislikes = 0;
        $comments = 0;

        // Validate required fields
        if (!$student_id || empty($title) || empty($description) || empty($category)) {
            throw new Exception("Please fill in all required fields.");
        }

        // Prepare SQL statement
        $sql = "INSERT INTO pitches (
            student_id, title, description, category, keywords, video_url, ppt_url, poster_url,
            revenue_lakhs, expected_amount, expected_percentage, year_started, strength, weakness,
            risk_factors, date_uploaded, status, visibility, student_percentage, no_of_investors,
            no_of_investors_accepted, likes, dislikes, comments
        ) VALUES (
            :student_id, :title, :description, :category, :keywords, :video_url, :ppt_url, :poster_url,
            :revenue_lakhs, :expected_amount, :expected_percentage, :year_started, :strength, :weakness,
            :risk_factors, :date_uploaded, :status, :visibility, :student_percentage, :no_of_investors,
            :no_of_investors_accepted, :likes, :dislikes, :comments
        )";

        $stmt = $pdo->prepare($sql);
        
        // Bind parameters
        $stmt->bindParam(':student_id', $student_id, PDO::PARAM_INT);
        $stmt->bindParam(':title', $title, PDO::PARAM_STR);
        $stmt->bindParam(':description', $description, PDO::PARAM_STR);
        $stmt->bindParam(':category', $category, PDO::PARAM_STR);
        $stmt->bindParam(':keywords', $keywords, PDO::PARAM_STR);
        $stmt->bindParam(':video_url', $video_url, PDO::PARAM_STR);
        $stmt->bindParam(':ppt_url', $ppt_url, PDO::PARAM_STR);
        $stmt->bindParam(':poster_url', $poster_url, PDO::PARAM_STR);
        $stmt->bindParam(':revenue_lakhs', $revenue_lakhs, PDO::PARAM_STR);
        $stmt->bindParam(':expected_amount', $expected_amount, PDO::PARAM_STR);
        $stmt->bindParam(':expected_percentage', $expected_percentage, PDO::PARAM_STR);
        $stmt->bindParam(':year_started', $year_started, PDO::PARAM_INT);
        $stmt->bindParam(':strength', $strength, PDO::PARAM_STR);
        $stmt->bindParam(':weakness', $weakness, PDO::PARAM_STR);
        $stmt->bindParam(':risk_factors', $risk_factors, PDO::PARAM_STR);
        $stmt->bindParam(':date_uploaded', $date_uploaded, PDO::PARAM_STR);
        $stmt->bindParam(':status', $status, PDO::PARAM_STR);
        $stmt->bindParam(':visibility', $visibility, PDO::PARAM_STR);
        $stmt->bindParam(':student_percentage', $student_percentage, PDO::PARAM_STR);
        $stmt->bindParam(':no_of_investors', $no_of_investors, PDO::PARAM_INT);
        $stmt->bindParam(':no_of_investors_accepted', $no_of_investors_accepted, PDO::PARAM_INT);
        $stmt->bindParam(':likes', $likes, PDO::PARAM_INT);
        $stmt->bindParam(':dislikes', $dislikes, PDO::PARAM_INT);
        $stmt->bindParam(':comments', $comments, PDO::PARAM_INT);

        // Execute the statement
        if ($stmt->execute()) {
            $pitch_id = $pdo->lastInsertId();
            $message = "Pitch submitted successfully! Your pitch ID is: " . $pitch_id;
            $messageType = 'success';
            
            // Clear form data by redirecting
            header("Location: " . $_SERVER['PHP_SELF'] . "?success=1");
            exit();
        } else {
            throw new Exception("Error submitting pitch. Please try again.");
        }

    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
        $messageType = 'error';
    }
}

// Check for success message from redirect
if (isset($_GET['success'])) {
    $message = "Pitch submitted successfully!";
    $messageType = 'success';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Campus Shark Tank - Submit Your Pitch</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🦈 Campus Shark Tank</h1>
            <p>Submit your innovative business pitch</p>
        </header>

        <?php if ($message): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <form id="pitchForm" action="submit_pitch.php" method="POST" enctype="multipart/form-data">
            <div class="form-section">
                <h2>Basic Information</h2>
                
                <div class="form-group">
                    <label for="student_id">Student ID *</label>
                    <input type="number" id="student_id" name="student_id" required>
                </div>

                <div class="form-group">
                    <label for="title">Pitch Title *</label>
                    <input type="text" id="title" name="title" maxlength="255" required>
                </div>

                <div class="form-group">
                    <label for="description">Description *</label>
                    <textarea id="description" name="description" rows="5" required placeholder="Provide a detailed description of your venture..."></textarea>
                </div>

                <div class="form-group">
                    <label for="category">Category *</label>
                    <select id="category" name="category" required>
                        <option value="">Select a category</option>
                        <option value="Technology">Technology</option>
                        <option value="Healthcare">Healthcare</option>
                        <option value="Education">Education</option>
                        <option value="Finance">Finance</option>
                        <option value="E-commerce">E-commerce</option>
                        <option value="Food & Beverage">Food & Beverage</option>
                        <option value="Entertainment">Entertainment</option>
                        <option value="Social Impact">Social Impact</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="keywords">Keywords</label>
                    <input type="text" id="keywords" name="keywords" maxlength="255" placeholder="Comma-separated tags (e.g., AI, mobile app, sustainability)">
                </div>
            </div>

            <div class="form-section">
                <h2>Media & Presentation</h2>
                
                <div class="form-group">
                    <label for="video_url">Video URL</label>
                    <input type="url" id="video_url" name="video_url" maxlength="255" placeholder="https://youtube.com/watch?v=...">
                </div>

                <div class="form-group">
                    <label for="ppt_url">Presentation URL</label>
                    <input type="url" id="ppt_url" name="ppt_url" maxlength="255" placeholder="https://drive.google.com/...">
                </div>

                <div class="form-group">
                    <label for="poster_url">Poster Image URL</label>
                    <input type="url" id="poster_url" name="poster_url" maxlength="255" placeholder="https://example.com/poster.jpg">
                </div>
            </div>

            <div class="form-section">
                <h2>Financial Information</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="revenue_lakhs">Revenue (in Lakhs)</label>
                        <input type="number" id="revenue_lakhs" name="revenue_lakhs" step="0.01" min="0" placeholder="0.00">
                    </div>

                    <div class="form-group">
                        <label for="expected_amount">Expected Investment (in Lakhs)</label>
                        <input type="number" id="expected_amount" name="expected_amount" step="0.01" min="0" placeholder="0.00">
                    </div>
                </div>

                <div class="form-group">
                    <label for="expected_percentage">Expected Percentage</label>
                    <input type="number" id="expected_percentage" name="expected_percentage" step="0.01" min="0" max="100" placeholder="0.00">
                </div>

                <div class="form-group">
                    <label for="year_started">Year Started</label>
                    <input type="number" id="year_started" name="year_started" min="1900" max="2030" placeholder="2024">
                </div>
            </div>

            <div class="form-section">
                <h2>Business Analysis</h2>
                
                <div class="form-group">
                    <label for="strength">Strengths</label>
                    <textarea id="strength" name="strength" rows="3" placeholder="What are the key strengths of your venture?"></textarea>
                </div>

                <div class="form-group">
                    <label for="weakness">Weaknesses</label>
                    <textarea id="weakness" name="weakness" rows="3" placeholder="What challenges or weaknesses do you foresee?"></textarea>
                </div>

                <div class="form-group">
                    <label for="risk_factors">Risk Factors</label>
                    <textarea id="risk_factors" name="risk_factors" rows="3" placeholder="What are the potential risks involved?"></textarea>
                </div>
            </div>

            <div class="form-section">
                <h2>Visibility Settings</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="visibility">Visibility</label>
                        <select id="visibility" name="visibility">
                            <option value="public">Public</option>
                            <option value="private">Private</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <button type="reset" class="btn btn-secondary">Reset Form</button>
                <button type="submit" class="btn btn-primary">Submit Pitch</button>
            </div>
        </form>
    </div>

    <script src="script.js"></script>
</body>
</html>
