<?php
require_once 'config.php';

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Get database connection
        $pdo = getDBConnection();
        
        // Sanitize and validate input data
        $student_id = filter_input(INPUT_POST, 'student_id', FILTER_VALIDATE_INT);
        $title = trim($_POST['title']);
        $description = trim($_POST['description']);
        $category = trim($_POST['category']);
        $keywords = trim($_POST['keywords']);
        $video_url = filter_input(INPUT_POST, 'video_url', FILTER_VALIDATE_URL) ?: null;
        $ppt_url = filter_input(INPUT_POST, 'ppt_url', FILTER_VALIDATE_URL) ?: null;
        $poster_url = filter_input(INPUT_POST, 'poster_url', FILTER_VALIDATE_URL) ?: null;
        $revenue_lakhs = filter_input(INPUT_POST, 'revenue_lakhs', FILTER_VALIDATE_FLOAT) ?: null;
        $expected_amount = filter_input(INPUT_POST, 'expected_amount', FILTER_VALIDATE_FLOAT) ?: null;
        $expected_percentage = filter_input(INPUT_POST, 'expected_percentage', FILTER_VALIDATE_FLOAT) ?: null;
        $year_started = filter_input(INPUT_POST, 'year_started', FILTER_VALIDATE_INT) ?: null;
        $strength = trim($_POST['strength']);
        $weakness = trim($_POST['weakness']);
        $risk_factors = trim($_POST['risk_factors']);
        $status = $_POST['status'] ?? 'active';
        $visibility = $_POST['visibility'] ?? 'public';
        
        // Current date and time
        $date_uploaded = date('Y-m-d H:i:s');
        
        // Initialize auto-tracked fields
        $student_percentage = 100.00;
        $no_of_investors = 0;
        $no_of_investors_accepted = 0;
        $likes = 0;
        $dislikes = 0;
        $comments = 0;

        // Validate required fields
        if (!$student_id || empty($title) || empty($description) || empty($category)) {
            throw new Exception("Please fill in all required fields.");
        }

        // Prepare SQL statement
        $sql = "INSERT INTO pitches (
            student_id, title, description, category, keywords, video_url, ppt_url, poster_url,
            revenue_lakhs, expected_amount, expected_percentage, year_started, strength, weakness,
            risk_factors, date_uploaded, status, visibility, student_percentage, no_of_investors,
            no_of_investors_accepted, likes, dislikes, comments
        ) VALUES (
            :student_id, :title, :description, :category, :keywords, :video_url, :ppt_url, :poster_url,
            :revenue_lakhs, :expected_amount, :expected_percentage, :year_started, :strength, :weakness,
            :risk_factors, :date_uploaded, :status, :visibility, :student_percentage, :no_of_investors,
            :no_of_investors_accepted, :likes, :dislikes, :comments
        )";

        $stmt = $pdo->prepare($sql);
        
        // Bind parameters
        $stmt->bindParam(':student_id', $student_id, PDO::PARAM_INT);
        $stmt->bindParam(':title', $title, PDO::PARAM_STR);
        $stmt->bindParam(':description', $description, PDO::PARAM_STR);
        $stmt->bindParam(':category', $category, PDO::PARAM_STR);
        $stmt->bindParam(':keywords', $keywords, PDO::PARAM_STR);
        $stmt->bindParam(':video_url', $video_url, PDO::PARAM_STR);
        $stmt->bindParam(':ppt_url', $ppt_url, PDO::PARAM_STR);
        $stmt->bindParam(':poster_url', $poster_url, PDO::PARAM_STR);
        $stmt->bindParam(':revenue_lakhs', $revenue_lakhs, PDO::PARAM_STR);
        $stmt->bindParam(':expected_amount', $expected_amount, PDO::PARAM_STR);
        $stmt->bindParam(':expected_percentage', $expected_percentage, PDO::PARAM_STR);
        $stmt->bindParam(':year_started', $year_started, PDO::PARAM_INT);
        $stmt->bindParam(':strength', $strength, PDO::PARAM_STR);
        $stmt->bindParam(':weakness', $weakness, PDO::PARAM_STR);
        $stmt->bindParam(':risk_factors', $risk_factors, PDO::PARAM_STR);
        $stmt->bindParam(':date_uploaded', $date_uploaded, PDO::PARAM_STR);
        $stmt->bindParam(':status', $status, PDO::PARAM_STR);
        $stmt->bindParam(':visibility', $visibility, PDO::PARAM_STR);
        $stmt->bindParam(':student_percentage', $student_percentage, PDO::PARAM_STR);
        $stmt->bindParam(':no_of_investors', $no_of_investors, PDO::PARAM_INT);
        $stmt->bindParam(':no_of_investors_accepted', $no_of_investors_accepted, PDO::PARAM_INT);
        $stmt->bindParam(':likes', $likes, PDO::PARAM_INT);
        $stmt->bindParam(':dislikes', $dislikes, PDO::PARAM_INT);
        $stmt->bindParam(':comments', $comments, PDO::PARAM_INT);

        // Execute the statement
        if ($stmt->execute()) {
            $message = "Done";
            $messageType = 'success';
            
            // Clear form data by redirecting
            header("Location: " . $_SERVER['PHP_SELF'] . "?success=1");
            exit();
        } else {
            throw new Exception("Error submitting pitch. Please try again.");
        }

    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
        $messageType = 'error';
    }
}

// Check for success message from redirect
if (isset($_GET['success'])) {
    $message = "Done";
    $messageType = 'success';
}

require_once 'includes/header.php';
?>

<style>
.pitch-form {
    max-width: 800px;
    margin: 20px auto;
    padding: 30px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-section {
    margin-bottom: 30px;
}

.form-section h3 {
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
}

.form-group {
    margin-bottom: 15px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #555;
}

input, select, textarea {
    width: 100%;
    padding: 10px;
    border: 2px solid #e1e1e1;
    border-radius: 5px;
    font-size: 14px;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #007bff;
}

textarea {
    resize: vertical;
    min-height: 80px;
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    margin-right: 10px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.message {
    padding: 15px;
    margin: 20px 0;
    border-radius: 5px;
    font-weight: 500;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    .pitch-form {
        margin: 10px;
        padding: 20px;
    }
}
</style>

<div class="container">
    <div class="pitch-form">
        <h2>🦈 Submit Your Pitch</h2>
        
        <?php if ($message): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <div class="form-section">
                <h3>Basic Information</h3>
                
                <div class="form-group">
                    <label for="student_id">Student ID *</label>
                    <input type="number" id="student_id" name="student_id" required>
                </div>

                <div class="form-group">
                    <label for="title">Pitch Title *</label>
                    <input type="text" id="title" name="title" maxlength="255" required>
                </div>

                <div class="form-group">
                    <label for="description">Description *</label>
                    <textarea id="description" name="description" rows="4" required placeholder="Describe your business idea..."></textarea>
                </div>

                <div class="form-group">
                    <label for="category">Category *</label>
                    <select id="category" name="category" required>
                        <option value="">Select a category</option>
                        <option value="Technology">Technology</option>
                        <option value="Healthcare">Healthcare</option>
                        <option value="Education">Education</option>
                        <option value="Finance">Finance</option>
                        <option value="E-commerce">E-commerce</option>
                        <option value="Food & Beverage">Food & Beverage</option>
                        <option value="Entertainment">Entertainment</option>
                        <option value="Social Impact">Social Impact</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="keywords">Keywords</label>
                    <input type="text" id="keywords" name="keywords" maxlength="255" placeholder="e.g., AI, mobile app, sustainability">
                </div>
            </div>

            <div class="form-section">
                <h3>Media & Presentation</h3>
                
                <div class="form-group">
                    <label for="video_url">Video URL</label>
                    <input type="url" id="video_url" name="video_url" placeholder="https://youtube.com/watch?v=...">
                </div>

                <div class="form-group">
                    <label for="ppt_url">Presentation URL</label>
                    <input type="url" id="ppt_url" name="ppt_url" placeholder="https://drive.google.com/...">
                </div>

                <div class="form-group">
                    <label for="poster_url">Poster Image URL</label>
                    <input type="url" id="poster_url" name="poster_url" placeholder="https://example.com/poster.jpg">
                </div>
            </div>

            <div class="form-section">
                <h3>Financial Information</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="revenue_lakhs">Revenue (in Lakhs)</label>
                        <input type="number" id="revenue_lakhs" name="revenue_lakhs" step="0.01" min="0" placeholder="0.00">
                    </div>

                    <div class="form-group">
                        <label for="expected_amount">Expected Investment (in Lakhs)</label>
                        <input type="number" id="expected_amount" name="expected_amount" step="0.01" min="0" placeholder="0.00">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="expected_percentage">Expected Percentage</label>
                        <input type="number" id="expected_percentage" name="expected_percentage" step="0.01" min="0" max="100" placeholder="0.00">
                    </div>

                    <div class="form-group">
                        <label for="year_started">Year Started</label>
                        <input type="number" id="year_started" name="year_started" min="1900" max="2030" placeholder="2024">
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h3>Business Analysis</h3>
                
                <div class="form-group">
                    <label for="strength">Strengths</label>
                    <textarea id="strength" name="strength" rows="3" placeholder="Key strengths of your venture..."></textarea>
                </div>

                <div class="form-group">
                    <label for="weakness">Weaknesses</label>
                    <textarea id="weakness" name="weakness" rows="3" placeholder="Challenges or weaknesses..."></textarea>
                </div>

                <div class="form-group">
                    <label for="risk_factors">Risk Factors</label>
                    <textarea id="risk_factors" name="risk_factors" rows="3" placeholder="Potential risks..."></textarea>
                </div>
            </div>

            <div class="form-section">
                <h3>Settings</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="visibility">Visibility</label>
                        <select id="visibility" name="visibility">
                            <option value="public">Public</option>
                            <option value="private">Private</option>
                        </select>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button type="reset" class="btn btn-secondary">Reset Form</button>
                <button type="submit" class="btn btn-primary">Submit Pitch</button>
            </div>
        </form>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
