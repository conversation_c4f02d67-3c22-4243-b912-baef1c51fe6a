-- Campus Shark Tank Database Setup
-- Run this SQL script to create the database and table

-- <PERSON>reate database
CREATE DATABASE IF NOT EXISTS campus_shark_tank;
USE campus_shark_tank;

-- Create pitches table
CREATE TABLE IF NOT EXISTS pitches (
    pitch_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    keywords VARCHAR(255),
    video_url VARCHAR(255),
    ppt_url VARCHAR(255),
    poster_url VARCHAR(255),
    revenue_lakhs DECIMAL(10, 2),
    expected_amount DECIMAL(10, 2),
    expected_percentage DECIMAL(5, 2),
    year_started INT,
    strength TEXT,
    weakness TEXT,
    risk_factors TEXT,
    date_uploaded DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') DEFAULT 'active',
    visibility ENUM('public', 'private') DEFAULT 'public',
    student_percentage DECIMAL(5, 2) DEFAULT 100.00,
    no_of_investors INT DEFAULT 0,
    no_of_investors_accepted INT DEFAULT 0,
    likes INT DEFAULT 0,
    dislikes INT DEFAULT 0,
    comments INT DEFAULT 0,
    
    INDEX idx_student_id (student_id),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_visibility (visibility),
    INDEX idx_date_uploaded (date_uploaded)
);

-- Optional: Create a users table for student information
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    department VARCHAR(100),
    year_of_study INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_student_id (student_id),
    INDEX idx_email (email)
);

-- Optional: Create investors table
CREATE TABLE IF NOT EXISTS investors (
    investor_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    company VARCHAR(255),
    investment_capacity DECIMAL(12, 2),
    areas_of_interest TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_email (email)
);

-- Optional: Create investments table to track investor-pitch relationships
CREATE TABLE IF NOT EXISTS investments (
    investment_id INT AUTO_INCREMENT PRIMARY KEY,
    pitch_id INT NOT NULL,
    investor_id INT NOT NULL,
    amount_offered DECIMAL(10, 2),
    percentage_requested DECIMAL(5, 2),
    status ENUM('pending', 'accepted', 'rejected') DEFAULT 'pending',
    offer_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    response_date DATETIME,
    
    FOREIGN KEY (pitch_id) REFERENCES pitches(pitch_id) ON DELETE CASCADE,
    FOREIGN KEY (investor_id) REFERENCES investors(investor_id) ON DELETE CASCADE,
    
    INDEX idx_pitch_id (pitch_id),
    INDEX idx_investor_id (investor_id),
    INDEX idx_status (status)
);

-- Optional: Create comments table
CREATE TABLE IF NOT EXISTS pitch_comments (
    comment_id INT AUTO_INCREMENT PRIMARY KEY,
    pitch_id INT NOT NULL,
    user_id INT,
    investor_id INT,
    comment TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (pitch_id) REFERENCES pitches(pitch_id) ON DELETE CASCADE,
    
    INDEX idx_pitch_id (pitch_id),
    INDEX idx_created_at (created_at)
);

-- Optional: Create likes/dislikes table
CREATE TABLE IF NOT EXISTS pitch_reactions (
    reaction_id INT AUTO_INCREMENT PRIMARY KEY,
    pitch_id INT NOT NULL,
    user_id INT,
    investor_id INT,
    reaction_type ENUM('like', 'dislike') NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (pitch_id) REFERENCES pitches(pitch_id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_reaction (pitch_id, user_id, reaction_type),
    UNIQUE KEY unique_investor_reaction (pitch_id, investor_id, reaction_type),
    
    INDEX idx_pitch_id (pitch_id),
    INDEX idx_reaction_type (reaction_type)
);

-- Insert sample data (optional)
INSERT INTO users (student_id, name, email, department, year_of_study) VALUES
(12345, 'John Doe', '<EMAIL>', 'Computer Science', 3),
(12346, 'Jane Smith', '<EMAIL>', 'Business Administration', 4),
(12347, 'Mike Johnson', '<EMAIL>', 'Engineering', 2);

INSERT INTO investors (name, email, company, investment_capacity) VALUES
('Sarah Wilson', '<EMAIL>', 'Venture Capital Partners', 50000000.00),
('David Chen', '<EMAIL>', 'Angel Investors Group', 25000000.00),
('Lisa Rodriguez', '<EMAIL>', 'Tech Innovation Fund', 75000000.00);

-- Show table structure
DESCRIBE pitches;
