<?php
require_once '../config.php';
require_once '../db.php';

// Check if investor is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'investor') {
    header('Location: ../auth/login.php');
    exit;
}

// Get pitch ID
$pitch_id = isset($_POST['pitch_id']) ? (int)$_POST['pitch_id'] : 0;

if ($pitch_id <= 0) {
    header('Location: dashboard.php');
    exit;
}

// Check if investor has already liked this pitch
$investor_id = $_SESSION['user_id'];
$checkLikeQuery = "SELECT * FROM votes WHERE user_id = $investor_id AND pitch_id = $pitch_id";
$checkLikeResult = $conn->query($checkLikeQuery);

if ($checkLikeResult->num_rows == 0) {
    // Add like
    $insertLikeQuery = "INSERT INTO votes (user_id, pitch_id, vote_value) VALUES ($investor_id, $pitch_id, 1)";
    
    if ($conn->query($insertLikeQuery) === TRUE) {
        // Update likes count in pitches table
        $updateLikesQuery = "UPDATE pitches SET likes = likes + 1 WHERE pitch_id = $pitch_id";
        $conn->query($updateLikesQuery);
    }
}

// Redirect back to pitch detail
header("Location: pitch_detail.php?id=$pitch_id");
exit;
?>