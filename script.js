// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('pitchForm');
    const submitBtn = form.querySelector('.btn-primary');
    
    // Form submission handling
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return false;
        }
        
        // Show loading state
        submitBtn.textContent = 'Submitting...';
        submitBtn.classList.add('loading');
        form.classList.add('loading');
    });

    // Real-time validation
    const requiredFields = ['student_id', 'title', 'description', 'category'];
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('blur', validateField);
            field.addEventListener('input', clearFieldError);
        }
    });

    // URL validation for media fields
    const urlFields = ['video_url', 'ppt_url', 'poster_url'];
    urlFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('blur', validateURL);
        }
    });

    // Numeric validation
    const numericFields = ['revenue_lakhs', 'expected_amount', 'expected_percentage'];
    numericFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', validateNumeric);
        }
    });

    // Year validation
    const yearField = document.getElementById('year_started');
    if (yearField) {
        yearField.addEventListener('input', validateYear);
    }

    // Character count for text areas
    const textAreas = document.querySelectorAll('textarea');
    textAreas.forEach(textarea => {
        addCharacterCounter(textarea);
    });

    function validateForm() {
        let isValid = true;
        
        // Validate required fields
        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (!field.value.trim()) {
                showFieldError(field, 'This field is required');
                isValid = false;
            }
        });

        // Validate student ID
        const studentId = document.getElementById('student_id');
        if (studentId.value && (studentId.value < 1 || studentId.value > 999999999)) {
            showFieldError(studentId, 'Please enter a valid student ID');
            isValid = false;
        }

        // Validate URLs
        urlFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field.value && !isValidURL(field.value)) {
                showFieldError(field, 'Please enter a valid URL');
                isValid = false;
            }
        });

        // Validate percentage
        const percentage = document.getElementById('expected_percentage');
        if (percentage.value && (percentage.value < 0 || percentage.value > 100)) {
            showFieldError(percentage, 'Percentage must be between 0 and 100');
            isValid = false;
        }

        return isValid;
    }

    function validateField(e) {
        const field = e.target;
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
        } else {
            clearFieldError(field);
        }
    }

    function validateURL(e) {
        const field = e.target;
        if (field.value && !isValidURL(field.value)) {
            showFieldError(field, 'Please enter a valid URL');
        } else {
            clearFieldError(field);
        }
    }

    function validateNumeric(e) {
        const field = e.target;
        if (field.value && field.value < 0) {
            showFieldError(field, 'Value cannot be negative');
        } else {
            clearFieldError(field);
        }
    }

    function validateYear(e) {
        const field = e.target;
        const currentYear = new Date().getFullYear();
        if (field.value && (field.value < 1900 || field.value > currentYear + 10)) {
            showFieldError(field, `Year must be between 1900 and ${currentYear + 10}`);
        } else {
            clearFieldError(field);
        }
    }

    function isValidURL(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    function showFieldError(field, message) {
        clearFieldError(field);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.style.color = '#dc3545';
        errorDiv.style.fontSize = '14px';
        errorDiv.style.marginTop = '5px';
        errorDiv.textContent = message;
        
        field.style.borderColor = '#dc3545';
        field.parentNode.appendChild(errorDiv);
    }

    function clearFieldError(field) {
        const errorDiv = field.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
        field.style.borderColor = '';
    }

    function addCharacterCounter(textarea) {
        const maxLength = textarea.getAttribute('maxlength');
        if (!maxLength) return;

        const counter = document.createElement('div');
        counter.className = 'char-counter';
        counter.style.textAlign = 'right';
        counter.style.fontSize = '12px';
        counter.style.color = '#666';
        counter.style.marginTop = '5px';
        
        function updateCounter() {
            const remaining = maxLength - textarea.value.length;
            counter.textContent = `${textarea.value.length}/${maxLength} characters`;
            counter.style.color = remaining < 50 ? '#dc3545' : '#666';
        }

        textarea.addEventListener('input', updateCounter);
        textarea.parentNode.appendChild(counter);
        updateCounter();
    }

    // Auto-save form data to localStorage
    const formData = {};
    const inputs = form.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        // Load saved data
        const savedValue = localStorage.getItem(`pitch_form_${input.name}`);
        if (savedValue && input.type !== 'submit' && input.type !== 'reset') {
            input.value = savedValue;
        }

        // Save data on change
        input.addEventListener('input', function() {
            if (input.type !== 'submit' && input.type !== 'reset') {
                localStorage.setItem(`pitch_form_${input.name}`, input.value);
            }
        });
    });

    // Clear saved data on successful submission
    form.addEventListener('submit', function() {
        setTimeout(() => {
            inputs.forEach(input => {
                localStorage.removeItem(`pitch_form_${input.name}`);
            });
        }, 1000);
    });

    // Reset form and clear saved data
    const resetBtn = form.querySelector('button[type="reset"]');
    if (resetBtn) {
        resetBtn.addEventListener('click', function() {
            inputs.forEach(input => {
                localStorage.removeItem(`pitch_form_${input.name}`);
                clearFieldError(input);
            });
        });
    }
});

// Smooth scrolling for form sections
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({ behavior: 'smooth' });
    }
}

// Add loading animation
const style = document.createElement('style');
style.textContent = `
    .loading .btn-primary {
        position: relative;
        color: transparent;
    }
    
    .loading .btn-primary::after {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-left: -8px;
        margin-top: -8px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
`;
document.head.appendChild(style);
