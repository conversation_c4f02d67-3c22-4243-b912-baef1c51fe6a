<?php
require_once '../config.php';
require_once '../db.php';
require_once '../includes/header.php';

// Check if investor is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'investor') {
    header('Location: ../auth/login.php');
    exit;
}

// Get pitch ID
$pitch_id = isset($_GET['pitch_id']) ? (int)$_GET['pitch_id'] : 0;

if ($pitch_id <= 0) {
    header('Location: dashboard.php');
    exit;
}

// Get pitch details
$query = "SELECT * FROM pitches WHERE pitch_id = $pitch_id AND status = 'approved'";
$result = $conn->query($query);

if ($result->num_rows == 0) {
    header('Location: dashboard.php');
    exit;
}

$pitch = $result->fetch_assoc();

// Check if investor has already made an offer that is pending or accepted
$investor_id = $_SESSION['user_id'];
$checkOfferQuery = "SELECT * FROM negotiations WHERE pitch_id = $pitch_id AND investor_id = $investor_id AND status IN ('pending', 'accepted', 'funded')";
$checkOfferResult = $conn->query($checkOfferQuery);

if ($checkOfferResult->num_rows > 0) {
    $existingOffer = $checkOfferResult->fetch_assoc();
    echo "<div class='container'><div class='card'><h1>Existing Offer</h1>";
    echo "<p>You have already made an offer for this pitch. Status: " . ucfirst($existingOffer['status']) . "</p>";
    echo "<p>Amount: $" . number_format($existingOffer['proposed_amount'], 2) . ", Equity: " . $existingOffer['proposed_equity'] . "%</p>";
    echo "<a href='dashboard.php' class='btn'>Back to Dashboard</a></div></div>";
    require_once '../includes/footer.php';
    exit;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $amount = (float)$_POST['amount'];
    $equity = (float)$_POST['equity'];
    
    // Validate
    if ($amount <= 0 || $equity <= 0) {
        $error = "Amount and equity must be greater than 0.";
    } else {
        // Insert negotiation
        $insertQuery = "INSERT INTO negotiations (pitch_id, student_id, investor_id, proposed_amount, proposed_equity, status) 
                        VALUES ($pitch_id, " . $pitch['user_id'] . ", $investor_id, $amount, $equity, 'pending')";
        
        if ($conn->query($insertQuery) === TRUE) {
            $success = "Your offer has been sent to the entrepreneur.";
        } else {
            $error = "Error: " . $conn->error;
        }
    }
}
?>

<div class="container">
    <div class="card">
        <h1>Make an Offer</h1>
        <h2><?php echo $pitch['title']; ?></h2>
        <p><strong>Seeking Amount:</strong> $<?php echo number_format($pitch['funding_goal'], 2); ?></p>
        <p><strong>Offered Equity:</strong> <?php echo $pitch['equity_offered']; ?>%</p>
        
        <?php if (isset($error)) { ?>
            <div class="error"><?php echo $error; ?></div>
        <?php } ?>
        
        <?php if (isset($success)) { ?>
            <div class="success"><?php echo $success; ?></div>
            <a href="dashboard.php" class="btn">Back to Dashboard</a>
        <?php } else { ?>
            <form method="POST" action="">
                <div class="form-group">
                    <label for="amount">Your Investment Amount ($)</label>
                    <input type="number" id="amount" name="amount" min="1" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="equity">Equity You Want (%)</label>
                    <input type="number" id="equity" name="equity" min="0.1" max="100" step="0.1" required>
                </div>
                <button type="submit" class="btn">Submit Offer</button>
                <a href="pitch_detail.php?id=<?php echo $pitch_id; ?>" class="btn btn-secondary">Cancel</a>
            </form>
        <?php } ?>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>