<?php
require_once '../config.php';
require_once '../db.php';
require_once '../includes/header.php';

// Check if investor is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'investor') {
    header('Location: ../auth/login.php');
    exit;
}

// Get filter parameters
$category = isset($_GET['category']) ? $_GET['category'] : '';

// Build query
$query = "SELECT * FROM pitches WHERE status = 'approved'";
if (!empty($category)) {
    $query .= " AND category = '$category'";
}
$query .= " ORDER BY likes DESC"; // Order by most liked

$result = $conn->query($query);

// Get unique categories for filter
$categoryQuery = "SELECT DISTINCT category FROM pitches WHERE status = 'approved'";
$categoryResult = $conn->query($categoryQuery);
?>

<div class="container">
    <h1>Investor Dashboard</h1>
    
    <!-- Filter Section -->
    <div class="card">
        <h2>Filter Pitches</h2>
        <form method="GET" action="">
            <div class="form-group">
                <label for="category">Category</label>
                <select name="category" id="category">
                    <option value="">All Categories</option>
                    <?php while ($cat = $categoryResult->fetch_assoc()) { ?>
                        <option value="<?php echo $cat['category']; ?>" <?php echo ($category == $cat['category']) ? 'selected' : ''; ?>>
                            <?php echo $cat['category']; ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
            <button type="submit" class="btn">Apply Filter</button>
        </form>
    </div>
    
    <!-- Pitches List -->
    <div class="card">
        <h2>Available Pitches</h2>
        <?php if ($result->num_rows > 0) { ?>
            <div class="pitches-list">
                <?php while ($pitch = $result->fetch_assoc()) { ?>
                    <div class="pitch-card">
                        <h3><?php echo $pitch['title']; ?></h3>
                        <p><strong>Category:</strong> <?php echo $pitch['category']; ?></p>
                        <p><strong>Seeking Amount:</strong> $<?php echo number_format($pitch['funding_goal'], 2); ?></p>
                        <p><strong>Offered Equity:</strong> <?php echo $pitch['equity_offered']; ?>%</p>
                        <p><strong>Likes:</strong> <?php echo $pitch['likes']; ?></p>
                        <div class="actions">
                            <a href="pitch_detail.php?id=<?php echo $pitch['pitch_id']; ?>" class="btn">View Details</a>
                            <a href="negotiate.php?pitch_id=<?php echo $pitch['pitch_id']; ?>" class="btn btn-secondary">Negotiate</a>
                        </div>
                    </div>
                <?php } ?>
            </div>
        <?php } else { ?>
            <p>No pitches found.</p>
        <?php } ?>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>